/**
 * 实体组件比较面板组件
 * 用于比较两个实体之间的组件差异
 */
import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Tabs, 
  Typography, 
  Tag, 
  Button, 
  Space, 
  Tooltip, 
  Collapse,
  Empty,
  Divider,
  Badge,
  Switch
} from 'antd';
import {
  SwapOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  MinusOutlined,
  EditOutlined,
  DownloadOutlined,
  EyeOutlined,
  CodeOutlined,
  DiffOutlined,
  TableOutlined
} from '@ant-design/icons';
import JsonView from '../common/JsonView';
import './EntityComponentComparePanel.less';

const { Text, Title } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

// 组件属性接口
interface EntityComponentComparePanelProps {
  entity1: any;
  entity2: any;
  entity1Version: string;
  entity2Version: string;
  onClose: () => void;
  onExportComparison: (format: 'json' | 'html') => void;
}

/**
 * 实体组件比较面板组件
 */
const EntityComponentComparePanel: React.FC<EntityComponentComparePanelProps> = ({
  entity1,
  entity2,
  entity1Version,
  entity2Version,
  onClose,
  onExportComparison
}) => {
  const [activeTab, setActiveTab] = useState<string>('table');
  const [showOnlyDifferences, setShowOnlyDifferences] = useState<boolean>(true);
  
  // 如果没有实体数据，显示空状态
  if (!entity1 || !entity2) {
    return (
      <div className="entity-component-compare-panel">
        <Card
          title={
            <Space>
              <SwapOutlined />
              <span>组件比较</span>
            </Space>
          }
          className="compare-card"
        >
          <Empty description="没有可用的比较数据" />
        </Card>
      </div>
    );
  }
  
  // 获取所有组件类型
  const getAllComponentTypes = () => {
    const components1 = entity1.components || {};
    const components2 = entity2.components || {};
    
    const allTypes = new Set<string>();
    
    // 添加所有组件类型
    Object.keys(components1).forEach(type => allTypes.add(type));
    Object.keys(components2).forEach(type => allTypes.add(type));
    
    return Array.from(allTypes);
  };
  
  // 比较组件
  const compareComponents = () => {
    const components1 = entity1.components || {};
    const components2 = entity2.components || {};
    const allTypes = getAllComponentTypes();
    
    const result = {
      added: [] as string[],
      removed: [] as string[],
      modified: [] as {type: string, changes: any}[],
      unchanged: [] as string[]
    };
    
    allTypes.forEach(type => {
      const hasInEntity1 = type in components1;
      const hasInEntity2 = type in components2;
      
      if (!hasInEntity1 && hasInEntity2) {
        // 添加的组件
        result.added.push(type);
      } else if (hasInEntity1 && !hasInEntity2) {
        // 删除的组件
        result.removed.push(type);
      } else if (hasInEntity1 && hasInEntity2) {
        // 比较组件属性
        const comp1 = components1[type];
        const comp2 = components2[type];
        
        const changes = compareComponentProperties(comp1, comp2);
        
        if (Object.keys(changes).length > 0) {
          // 修改的组件
          result.modified.push({
            type,
            changes
          });
        } else {
          // 未修改的组件
          result.unchanged.push(type);
        }
      }
    });
    
    return result;
  };
  
  // 比较组件属性
  const compareComponentProperties = (comp1: any, comp2: any) => {
    const changes: any = {
      added: {},
      removed: {},
      modified: {}
    };
    
    // 检查添加和修改的属性
    for (const key in comp2) {
      if (!(key in comp1)) {
        changes.added[key] = comp2[key];
      } else if (JSON.stringify(comp1[key]) !== JSON.stringify(comp2[key])) {
        changes.modified[key] = {
          from: comp1[key],
          to: comp2[key]
        };
      }
    }
    
    // 检查删除的属性
    for (const key in comp1) {
      if (!(key in comp2)) {
        changes.removed[key] = comp1[key];
      }
    }
    
    return changes;
  };
  
  // 获取组件比较结果
  const comparisonResult = compareComponents();
  
  // 准备表格数据
  const prepareTableData = () => {
    const allTypes = getAllComponentTypes();
    const components1 = entity1.components || {};
    const components2 = entity2.components || {};
    
    return allTypes.map(type => {
      const hasInEntity1 = type in components1;
      const hasInEntity2 = type in components2;
      const isModified = comparisonResult.modified.some(item => item.type === type);
      
      // 如果只显示差异且组件未修改，则跳过
      if (showOnlyDifferences && hasInEntity1 && hasInEntity2 && !isModified) {
        return null;
      }
      
      return {
        key: type,
        componentType: type,
        status: !hasInEntity1 ? 'added' : !hasInEntity2 ? 'removed' : isModified ? 'modified' : 'unchanged',
        entity1Value: hasInEntity1 ? components1[type] : null,
        entity2Value: hasInEntity2 ? components2[type] : null};
    }).filter(Boolean);
  };
  
  // 渲染表格视图
  const renderTableView = () => {
    const tableData = prepareTableData();
    
    const columns = [
      {
        title: '组件类型',
        dataIndex: 'componentType',
        key: 'componentType',
        width: '20%',
        render: (text: string, record: any) => {
          let icon = null;
          let color = '';
          
          if (record.status === 'added') {
            icon = <PlusOutlined />;
            color = 'green';
          } else if (record.status === 'removed') {
            icon = <MinusOutlined />;
            color = 'red';
          } else if (record.status === 'modified') {
            icon = <EditOutlined />;
            color = 'blue';
          }
          
          return (
            <Space>
              {icon && <Text style={{ color }}>{icon}</Text>}
              <Text>{text}</Text>
            </Space>
          );
        }
      },
      {
        title: `版本1 (${entity1Version})`,
        dataIndex: 'entity1Value',
        key: 'entity1Value',
        width: '40%',
        render: (value: any) => {
          if (value === null) {
            return <Tag color="red">不存在</Tag>;
          }

          return (
            <Collapse ghost>
              <Panel header="查看组件数据" key="1">
                <JsonView
                  data={value}
                  expandAll={false}
                  showDataTypes={false}
                />
              </Panel>
            </Collapse>
          );
        }
      },
      {
        title: `版本2 (${entity2Version})`,
        dataIndex: 'entity2Value',
        key: 'entity2Value',
        width: '40%',
        render: (value: any) => {
          if (value === null) {
            return <Tag color="red">不存在</Tag>;
          }

          return (
            <Collapse ghost>
              <Panel header="查看组件数据" key="1">
                <JsonView
                  data={value}
                  expandAll={false}
                  showDataTypes={false}
                />
              </Panel>
            </Collapse>
          );
        }
      }
    ];
    
    return (
      <div className="table-view">
        <div className="table-header">
          <Space>
            <Switch 
              checked={showOnlyDifferences} 
              onChange={setShowOnlyDifferences} 
            />
            <Text>只显示差异</Text>
          </Space>
        </div>
        <Table 
          dataSource={tableData} 
          columns={columns} 
          pagination={false}
          expandable={{
            expandedRowRender: record => {
              if (!record || record.status !== 'modified') {
                return null;
              }

              const modifiedComponent = comparisonResult.modified.find(item => item.type === record.componentType);
              if (!modifiedComponent) {
                return null;
              }

              return (
                <div className="component-changes">
                  <Title level={5}>属性变更</Title>
                  {Object.keys(modifiedComponent.changes.added).length > 0 && (
                    <div className="change-section">
                      <Text strong>添加的属性:</Text>
                      <JsonView
                        data={modifiedComponent.changes.added}
                        expandAll={false}
                        showDataTypes={false}
                      />
                    </div>
                  )}

                  {Object.keys(modifiedComponent.changes.removed).length > 0 && (
                    <div className="change-section">
                      <Text strong>删除的属性:</Text>
                      <JsonView
                        data={modifiedComponent.changes.removed}
                        expandAll={false}
                        showDataTypes={false}
                      />
                    </div>
                  )}
                  
                  {Object.keys(modifiedComponent.changes.modified).length > 0 && (
                    <div className="change-section">
                      <Text strong>修改的属性:</Text>
                      <Table 
                        dataSource={Object.entries(modifiedComponent.changes.modified).map(([key, value]: [string, any]) => ({
                          key,
                          property: key,
                          oldValue: value.from,
                          newValue: value.to
                        }))}
                        columns={[
                          {
                            title: '属性',
                            dataIndex: 'property',
                            key: 'property',
                            width: '20%'
                          },
                          {
                            title: '旧值',
                            dataIndex: 'oldValue',
                            key: 'oldValue',
                            width: '40%',
                            render: value => <JsonView data={value} expandAll={false} showDataTypes={false} />
                          },
                          {
                            title: '新值',
                            dataIndex: 'newValue',
                            key: 'newValue',
                            width: '40%',
                            render: value => <JsonView data={value} expandAll={false} showDataTypes={false} />
                          }
                        ]}
                        pagination={false}
                        size="small"
                      />
                    </div>
                  )}
                </div>
              );
            }
          }}
        />
      </div>
    );
  };
  
  // 渲染JSON视图
  const renderJsonView = () => {
    return (
      <div className="json-view">
        <div className="json-container">
          <div className="json-column">
            <Title level={5}>版本1 ({entity1Version})</Title>
            <JsonView
              data={entity1.components || {}}
              expandAll={false}
              showDataTypes={false}
            />
          </div>
          <Divider type="vertical" className="json-divider" />
          <div className="json-column">
            <Title level={5}>版本2 ({entity2Version})</Title>
            <JsonView
              data={entity2.components || {}}
              expandAll={false}
              showDataTypes={false}
            />
          </div>
        </div>
      </div>
    );
  };
  
  // 渲染差异摘要
  const renderSummary = () => {
    return (
      <div className="comparison-summary">
        <Space size="large">
          <Badge count={comparisonResult.added.length} showZero>
            <Tag color="green" icon={<PlusOutlined />}>添加的组件</Tag>
          </Badge>
          <Badge count={comparisonResult.removed.length} showZero>
            <Tag color="red" icon={<MinusOutlined />}>删除的组件</Tag>
          </Badge>
          <Badge count={comparisonResult.modified.length} showZero>
            <Tag color="blue" icon={<EditOutlined />}>修改的组件</Tag>
          </Badge>
          <Badge count={comparisonResult.unchanged.length} showZero>
            <Tag color="default" icon={<CheckCircleOutlined />}>未修改的组件</Tag>
          </Badge>
        </Space>
      </div>
    );
  };
  
  return (
    <div className="entity-component-compare-panel">
      <Card
        title={
          <Space>
            <SwapOutlined />
            <span>实体组件比较: {entity1.name || entity1.id}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="导出为JSON">
              <Button 
                type="text" 
                icon={<DownloadOutlined />} 
                onClick={() => onExportComparison('json')}
              />
            </Tooltip>
            <Tooltip title="导出为HTML报告">
              <Button 
                type="text" 
                icon={<CodeOutlined />} 
                onClick={() => onExportComparison('html')}
              />
            </Tooltip>
            <Button type="text" onClick={onClose}>关闭</Button>
          </Space>
        }
        className="compare-card"
      >
        {renderSummary()}
        
        <Divider />
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <TableOutlined />
                表格视图
              </span>
            } 
            key="table"
          >
            {renderTableView()}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <CodeOutlined />
                JSON视图
              </span>
            } 
            key="json"
          >
            {renderJsonView()}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <DiffOutlined />
                差异视图
              </span>
            } 
            key="diff"
          >
            <div className="diff-view">
              <JsonView
                data={comparisonResult}
                expandAll={false}
                showDataTypes={false}
              />
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default EntityComponentComparePanel;
